#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check the default summary apps in Firestore database.
This script queries the database for the default conversation summary apps
and displays their prompts and configurations.
"""

import os
import sys
from google.cloud import firestore

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize Firebase
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = '/home/<USER>/omi/backend/google-credentials.json'
db = firestore.Client()

# Default app IDs from the environment variable or hardcoded fallback
DEFAULT_APP_IDS = ['summary_assistant', 'action_item_extractor', 'insight_analyzer']

def list_all_apps():
    """List all apps in the Firestore database"""
    print("🔍 LISTING ALL APPS IN FIRESTORE")
    print("=" * 60)

    apps_collection = 'plugins_data'

    try:
        # Get all documents in the apps collection
        apps_ref = db.collection(apps_collection).stream()
        apps_found = []

        for doc in apps_ref:
            app_data = doc.to_dict()
            apps_found.append({
                'id': doc.id,
                'name': app_data.get('name', 'Unknown'),
                'description': app_data.get('description', 'No description'),
                'has_memory_prompt': bool(app_data.get('memory_prompt')),
                'has_chat_prompt': bool(app_data.get('chat_prompt')),
                'enabled': app_data.get('enabled', False),
                'category': app_data.get('category', 'Unknown')
            })

        if apps_found:
            print(f"Found {len(apps_found)} apps in database:")
            for app in apps_found:
                print(f"\n📱 App ID: {app['id']}")
                print(f"   Name: {app['name']}")
                print(f"   Description: {app['description'][:100]}{'...' if len(app['description']) > 100 else ''}")
                print(f"   Has Memory Prompt: {app['has_memory_prompt']}")
                print(f"   Has Chat Prompt: {app['has_chat_prompt']}")
                print(f"   Enabled: {app['enabled']}")
                print(f"   Category: {app['category']}")
        else:
            print("❌ No apps found in database")

    except Exception as e:
        print(f"❌ Error listing apps: {e}")

def check_default_apps():
    """Check the default apps in Firestore database"""
    print("\n🔍 CHECKING DEFAULT SUMMARY APPS IN FIRESTORE")
    print("=" * 60)

    apps_collection = 'plugins_data'

    for app_id in DEFAULT_APP_IDS:
        print(f"\n📱 Checking app: {app_id}")
        print("-" * 40)

        try:
            # Get the app document from Firestore
            app_ref = db.collection(apps_collection).document(app_id)
            doc = app_ref.get()

            if doc.exists:
                app_data = doc.to_dict()
                print(f"✅ App found: {app_data.get('name', 'Unknown')}")
                print(f"📝 Description: {app_data.get('description', 'No description')}")

                # Check for different types of prompts
                memory_prompt = app_data.get('memory_prompt')
                chat_prompt = app_data.get('chat_prompt')
                persona_prompt = app_data.get('persona_prompt')

                if memory_prompt:
                    print(f"\n🧠 Memory Prompt:")
                    print(f"{'='*20}")
                    print(memory_prompt)
                    print(f"{'='*20}")

                if chat_prompt:
                    print(f"\n💬 Chat Prompt:")
                    print(f"{'='*20}")
                    print(chat_prompt)
                    print(f"{'='*20}")

                if persona_prompt:
                    print(f"\n👤 Persona Prompt:")
                    print(f"{'='*20}")
                    print(persona_prompt)
                    print(f"{'='*20}")

                # Show other relevant fields
                print(f"\n📊 Other fields:")
                print(f"   - Enabled: {app_data.get('enabled', 'Unknown')}")
                print(f"   - Category: {app_data.get('category', 'Unknown')}")
                print(f"   - Capabilities: {app_data.get('capabilities', [])}")
                print(f"   - Works with memories: {app_data.get('works_with_memories', 'Unknown')}")

            else:
                print(f"❌ App not found in database")

        except Exception as e:
            print(f"❌ Error checking app {app_id}: {e}")

    print(f"\n{'='*60}")
    print("✅ Default apps check completed")

def show_app_details(app_id):
    """Show detailed information about a specific app"""
    print(f"\n🔍 DETAILED VIEW OF APP: {app_id}")
    print("=" * 60)

    apps_collection = 'plugins_data'

    try:
        app_ref = db.collection(apps_collection).document(app_id)
        doc = app_ref.get()

        if doc.exists:
            app_data = doc.to_dict()
            print(f"✅ App found: {app_data.get('name', 'Unknown')}")
            print(f"📝 Description: {app_data.get('description', 'No description')}")

            # Show all fields
            for key, value in app_data.items():
                if key in ['memory_prompt', 'chat_prompt', 'persona_prompt'] and value:
                    print(f"\n🧠 {key.replace('_', ' ').title()}:")
                    print(f"{'='*50}")
                    print(value)
                    print(f"{'='*50}")
                else:
                    print(f"   {key}: {value}")
        else:
            print(f"❌ App not found")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    list_all_apps()
    check_default_apps()

    # Show details of apps with memory_prompt
    apps_to_check = [
        "01JYYAJWYGPGVHKEDZZEJKV50J",  # Teague summarizer
        "01JYYB2A3E5EDXNZ998K3MMYMH",  # AWS SA summarizer
        "01K1ZE23YGNYRHK9521KFYX71K",  # VC Interview Summerizer
    ]

    for app_id in apps_to_check:
        show_app_details(app_id)
